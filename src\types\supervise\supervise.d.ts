declare namespace SuperviseModule {
  interface SuperviseAccountRow {
    /** 账号ID */
    id: number;
    /** 账号名 */
    name: string;
    /** 监管数 */
    records: number;
    /** 是否记录传输文件 */
    is_record_file: boolean;
    /** 平台名称 */
    platform_name: string;
  }
  /** 账号ID */
  interface SuperviseMemberRow {
    id: number;
    /** 姓名 */
    name: string;
    /** 用户名 */
    username: string;
    /** 监管数 */
    records: number;
    /** 是否监管中 */
    in_supervise: boolean;
    /** 删除标记 0正常 1禁用 */
    delflag: number;
    /** 是否记录传输文件 */
    is_record_file: boolean;
    /** 角色ID */
    role_id: number;
    /** 角色名称 */
    role_name: string;
  }
  /** 修改监管网页监管状态 */
  interface IParamsSupervisePageEnable {
    account_ids: number[];
    enable: boolean;
    enable_is_all?: boolean;
  }
  export interface SuperviseUser {
    id: number;
    name: string;
    username: string;
    records: number;
    in_supervise: boolean;
  }

  export interface SuperviseUserListResponse {
    rows: SuperviseUser[];
    count: number;
  }
  interface LogListParams {
    account_name?: string;
    staff_name?: string;
    account_id?: number;
    staff_id?: number;
    warning_event_ids?: number[];
    start_time: number;
    end_time: number;
    page: number;
    limit: number;
  }
  interface GetAuthStaffParams {
    /** 关键字 */
    search_str?: string;
    /** 是否显示自己 */
    is_show_self?: 0 | 1;
    /** 是否显示禁用 */
    is_show_disable?: 0 | 1;
  }
  interface LogsItem {
    /** 账号ID */
    account_id: number;
    /** 用户ID */
    user_id: number;
    /** 会话ID */
    session_id: number;
    /** 账号名称 */
    account_name: string;
    /** 用户名称 */
    user_name: string;
    /** 用户用户名 */
    user_username: string;
    /** 监管开始时间 */
    start_time: number;
    /** 监管结束时间 */
    end_time: boolean;
    /** 风险事件次数 */
    warning_count: number;
    /** 机器码 */
    machine_string: string;
  }
  interface LogsDetailGroups {
    name: string;
    id: number;
    url: string;
    website_id: number;
    events: {
      /** 事件ID */
      event_id: string;
      /** 网站ID */
      website_id: number;
      /** 网页标题 */
      title: string;
      /** 网址URL */
      url: string;
      /** 事件类型 */
      type: string;
      /** 图片URL */
      image_url: string;
      /** 创建时间 */
      create_time: number;
      /** 是否为警告事件 */
      is_warning: 0 | 1;
      /** DOM内容 */
      dom: string;
      /** DOM ID */
      dom_id: number;
      /** 是否记录文件 */
      is_record_file: 0 | 1;
      /** 记录文件错误信息 */
      record_file_error: string;
      /** 是否敏感内容 */
      is_sensitive: boolean;
      /** 是否超时 */
      is_outtime: boolean;
      /** ID */
      id: number;
      /** 类型名称 */
      type_name: string;
      /** DOM名称 */
      dom_name: string | null;
      /** 是否全部 */
      is_all: 0 | 1;
    }[];
  }
  export interface LogsDetailResponse {
    account_name: string;
    account_platform_id: number;
    end_time: number;
    groups: LogsDetailGroups[];
    is_record_file: boolean;
    msg: string;
    start_time: number;
    supervise_day: number;
    url_group_id: number;
    user_name: string;
    user_username: string;
  }
}
