import React, { useEffect, useState, useRef } from 'react';
import { observer } from 'mobx-react';
import styles from './styles.module.scss';
import { Card, Toast } from 'antd-mobile';
import ClientRouter from '@/base/client/client-router';
import { useSearchParams } from 'react-router-dom';
import { superviseService } from '@/services/supervise';
import { to } from '@/utils/to';
import { gruops } from './mock';
import SwiperImages, { ImageItem, SwiperImagesRef } from '@/components/swiper-images';

const LogsDetail: React.FC = () => {
  const [searchParams] = useSearchParams();
  const clientRouter = ClientRouter.getRouter();
  const session_id = searchParams.get('session_id');
  const [logGroups, setLogGroups] = useState<SuperviseModule.LogsDetailGroups[]>(
    gruops as SuperviseModule.LogsDetailGroups[]
  );
  const swiperRef = useRef<SwiperImagesRef>(null);
  const [originData, setOriginData] = useState<SuperviseModule.LogsDetailResponse>();
  const [images, setImages] = useState<ImageItem[]>([]);

  const getLogDetail = async () => {
    if (session_id) {
      const [err, res] = await to<SuperviseModule.LogsDetailResponse>(
        superviseService.superviseLogDetail({ session_id })
      );
      if (err) return;
      console.log('@@@LogDetail-data', res);
      setOriginData(res);
      const imgs = [];
      res.groups.forEach((group) => {
        group.events.forEach((item) => {
          imgs.push({
            src: item.image_url,
            alt: item.title,
            thumbnail: item.image_url,
          });
        });
      });
      console.log('@@@@imgs', imgs);
    }
  };
  useEffect(() => {
    getLogDetail();
  }, []);

  console.log('@@@@logGroups', logGroups);
  return (
    <div className={styles.logsDetail}>
      <SwiperImages
        ref={swiperRef}
        images={[]}
        height="250px"
        autoplay={true}
        showToolbar={true}
        showRotateButton={true}
        showScaleButtons={true}
        showFullscreenButton={true}
        showLandscapeButton={true}
        showViewerPlayButton={true}
        viewerAutoplayInterval={2000}
        viewerAutoplayLoop={true}
        pauseViewerOnInteraction={true}
        className={styles.customSwiper}
      />
    </div>
  );
};

export default observer(LogsDetail);
